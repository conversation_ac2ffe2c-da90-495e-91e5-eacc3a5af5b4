/**
 * 视觉脚本数组节点
 * 提供数组操作相关的节点
 */
import { FunctionNode } from '../nodes/FunctionNode';
import { NodeCategory, NodeType, SocketDirection, SocketType } from '../nodes/Node';
import { NodeRegistry, NodeTypeInfo } from '../nodes/NodeRegistry';

/**
 * 数组创建节点
 * 创建一个新的数组
 */
export class ArrayCreateNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加初始大小输入
    this.addInput({
      name: 'size',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '初始大小',
      defaultValue: 0,
      optional: true
    });

    // 添加初始值输入
    this.addInput({
      name: 'initialValue',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '初始值',
      defaultValue: null,
      optional: true
    });

    // 添加数组输出
    this.addOutput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '创建的数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const size = Number(this.getInputValue('size') || 0);
    const initialValue = this.getInputValue('initialValue');

    // 创建数组
    let result: any[];
    if (size > 0) {
      result = new Array(size).fill(initialValue);
    } else {
      result = [];
    }

    // 设置输出值
    this.setOutputValue('array', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组添加元素节点
 * 向数组末尾添加元素
 */
export class ArrayPushNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加元素输入
    this.addInput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'any',
      description: '要添加的元素',
      defaultValue: null
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];
    const element = this.getInputValue('element');

    // 创建新数组副本并添加元素
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    result.push(element);

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 数组移除元素节点
 * 移除并返回数组末尾元素
 */
export class ArrayPopNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加数组输入
    this.addInput({
      name: 'array',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'array',
      description: '目标数组',
      defaultValue: []
    });

    // 添加结果输出
    this.addOutput({
      name: 'result',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'array',
      description: '修改后的数组'
    });

    // 添加移除的元素输出
    this.addOutput({
      name: 'element',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'any',
      description: '移除的元素'
    });

    // 添加长度输出
    this.addOutput({
      name: 'length',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '数组长度'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 获取输入值
    const inputArray = this.getInputValue('array') || [];

    // 创建新数组副本并移除元素
    const result = Array.isArray(inputArray) ? [...inputArray] : [];
    const element = result.pop();

    // 设置输出值
    this.setOutputValue('result', result);
    this.setOutputValue('element', element);
    this.setOutputValue('length', result.length);

    return result;
  }
}

/**
 * 注册数组节点
 * @param registry 节点注册表
 */
export function registerArrayNodes(registry: NodeRegistry): void {
  // 注册数组创建节点
  registry.registerNodeType({
    type: 'array/create',
    category: NodeCategory.ARRAY,
    constructor: ArrayCreateNode,
    label: '创建数组',
    description: '创建一个新的数组',
    icon: 'plus-square',
    color: '#1890FF',
    tags: ['array', 'create', 'new']
  });

  // 注册数组添加元素节点
  registry.registerNodeType({
    type: 'array/push',
    category: NodeCategory.ARRAY,
    constructor: ArrayPushNode,
    label: '添加元素',
    description: '向数组末尾添加元素',
    icon: 'plus-circle',
    color: '#1890FF',
    tags: ['array', 'push', 'add']
  });

  // 注册数组移除元素节点
  registry.registerNodeType({
    type: 'array/pop',
    category: NodeCategory.ARRAY,
    constructor: ArrayPopNode,
    label: '移除末尾元素',
    description: '移除并返回数组末尾元素',
    icon: 'minus-circle',
    color: '#1890FF',
    tags: ['array', 'pop', 'remove']
  });
}
